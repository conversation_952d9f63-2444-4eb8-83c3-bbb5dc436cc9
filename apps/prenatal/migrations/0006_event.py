# Generated by Django 5.2.4 on 2025-08-11 15:42

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('prenatal', '0005_split_datetime_to_date_time'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='事项的标题或名称', max_length=200, verbose_name='事项标题')),
                ('start_time', models.DateTimeField(help_text='事项的开始时间', verbose_name='开始时间')),
                ('end_time', models.DateTimeField(help_text='事项的结束时间', verbose_name='结束时间')),
                ('location', models.CharField(blank=True, help_text='事项发生的地点或位置', max_length=300, verbose_name='地点')),
                ('travel_duration', models.DurationField(blank=True, help_text='到达地点所需的出行时间（小时和分钟）', null=True, verbose_name='出行时间')),
                ('reminder_minutes', models.PositiveIntegerField(blank=True, help_text='提前多少分钟提醒（分钟数）', null=True, verbose_name='提醒时间')),
                ('notes', models.TextField(blank=True, help_text='事项的详细备注或说明', verbose_name='备注')),
                ('date', models.DateField(help_text='事项发生的日期', verbose_name='日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(help_text='创建该事项的用户', on_delete=django.db.models.deletion.CASCADE, related_name='events', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '事项',
                'verbose_name_plural': '事项',
                'db_table': 'events',
                'ordering': ['date', 'start_time'],
                'indexes': [models.Index(fields=['user', 'date', 'start_time'], name='events_user_id_765862_idx'), models.Index(fields=['date', 'start_time'], name='events_date_0c6c56_idx'), models.Index(fields=['user', 'start_time'], name='events_user_id_893d7c_idx')],
            },
        ),
    ]
