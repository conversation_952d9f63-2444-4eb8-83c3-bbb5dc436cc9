from django.contrib import admin
from django.utils.html import format_html
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup, Event
from .image_models import PrenatalImage, ImageUploadSession


@admin.register(PrenatalItem)
class PrenatalItemAdmin(admin.ModelAdmin):
    """标准产检项目管理"""

    list_display = ['name', 'week_range', 'created_at']
    list_filter = ['start_week', 'end_week', 'created_at']
    search_fields = ['name', 'content']
    ordering = ['start_week', 'name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'content')
        }),
        ('适用孕周', {
            'fields': ('start_week', 'end_week'),
            'description': '设置该项目适用的孕周范围（1-42周）'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def week_range(self, obj):
        """显示孕周范围"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'
    week_range.short_description = '适用孕周'


@admin.register(CustomPrenatalItem)
class CustomPrenatalItemAdmin(admin.ModelAdmin):
    """自定义产检项目管理"""

    list_display = ['name', 'created_by', 'week_range', 'created_at']
    list_filter = ['created_by', 'start_week', 'end_week', 'created_at']
    search_fields = ['name', 'content', 'created_by__email']
    ordering = ['created_by', 'start_week', 'name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'content', 'created_by')
        }),
        ('适用孕周', {
            'fields': ('start_week', 'end_week'),
            'description': '设置该项目适用的孕周范围（1-42周）'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def week_range(self, obj):
        """显示孕周范围"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'
    week_range.short_description = '适用孕周'


@admin.register(PrenatalCheckup)
class PrenatalCheckupAdmin(admin.ModelAdmin):
    """产检记录管理"""

    list_display = ['user_email', 'checkup_datetime', 'location', 'items_count', 'created_at']
    list_filter = ['date', 'created_at']
    search_fields = ['user__email', 'location', 'preparation_notes', 'checkup_notes']
    ordering = ['-date', '-time']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'date'

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'date', 'time', 'location')
        }),
        ('产检项目', {
            'fields': ('prenatal_items', 'custom_prenatal_items'),
            'description': '选择需要进行的产检项目'
        }),
        ('时间安排', {
            'fields': ('travel_time', 'reminder_time'),
            'classes': ('collapse',)
        }),
        ('笔记记录', {
            'fields': ('preparation_notes', 'checkup_notes'),
            'classes': ('collapse',)
        }),
        ('图片记录', {
            'fields': ('checkup_images',),
            'classes': ('collapse',),
            'description': '产检单、B超单等图片URL列表'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ['prenatal_items', 'custom_prenatal_items']

    def user_email(self, obj):
        """显示用户邮箱"""
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def checkup_datetime(self, obj):
        """显示产检日期时间"""
        if obj.date and obj.time:
            return f'{obj.date} {obj.time.strftime("%H:%M")}'
        elif obj.date:
            return str(obj.date)
        elif obj.time:
            return obj.time.strftime("%H:%M")
        return '-'
    checkup_datetime.short_description = '产检时间'

    def items_count(self, obj):
        """显示产检项目数量"""
        standard_count = obj.prenatal_items.count()
        custom_count = obj.custom_prenatal_items.count()
        total = standard_count + custom_count
        return format_html(
            '<span title="标准项目: {}, 自定义项目: {}">{}</span>',
            standard_count, custom_count, total
        )
    items_count.short_description = '项目数量'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user').prefetch_related(
            'prenatal_items', 'custom_prenatal_items'
        )


@admin.register(PrenatalImage)
class PrenatalImageAdmin(admin.ModelAdmin):
    """产检图片管理"""

    list_display = ['user_email', 'image_type', 'title', 'file_size_human', 'image_preview', 'checkup_info', 'created_at']
    list_filter = ['image_type', 'created_at', 'user']
    search_fields = ['user__email', 'title', 'description']
    ordering = ['-created_at']
    readonly_fields = ['file_size', 'width', 'height', 'created_at', 'updated_at', 'image_preview']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'image', 'image_preview', 'image_type', 'title', 'description')
        }),
        ('关联信息', {
            'fields': ('checkup',),
            'classes': ('collapse',)
        }),
        ('文件信息', {
            'fields': ('file_size', 'width', 'height'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def user_email(self, obj):
        """显示用户邮箱"""
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def file_size_human(self, obj):
        """显示人类可读的文件大小"""
        return obj.file_size_human
    file_size_human.short_description = '文件大小'

    def image_preview(self, obj):
        """显示图片预览"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.image.url
            )
        return '无图片'
    image_preview.short_description = '图片预览'

    def checkup_info(self, obj):
        """显示关联产检信息"""
        if obj.checkup:
            date_str = str(obj.checkup.date) if obj.checkup.date else '未设置'
            return format_html(
                '<span title="{}">{}</span>',
                obj.checkup.location,
                date_str
            )
        return '未关联'
    checkup_info.short_description = '关联产检'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user', 'checkup')


@admin.register(ImageUploadSession)
class ImageUploadSessionAdmin(admin.ModelAdmin):
    """图片上传会话管理"""

    list_display = ['user_email', 'session_name', 'status', 'progress_info', 'checkup_info', 'created_at']
    list_filter = ['status', 'created_at', 'user']
    search_fields = ['user__email', 'session_name']
    ordering = ['-created_at']
    readonly_fields = ['progress_percentage', 'is_completed', 'created_at', 'updated_at', 'completed_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'session_name', 'status')
        }),
        ('进度信息', {
            'fields': ('total_files', 'uploaded_files', 'failed_files', 'progress_percentage', 'is_completed')
        }),
        ('关联信息', {
            'fields': ('checkup',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )

    def user_email(self, obj):
        """显示用户邮箱"""
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def progress_info(self, obj):
        """显示进度信息"""
        return format_html(
            '<span title="成功: {}, 失败: {}">{:.1f}%</span>',
            obj.uploaded_files, obj.failed_files, obj.progress_percentage
        )
    progress_info.short_description = '上传进度'

    def checkup_info(self, obj):
        """显示关联产检信息"""
        if obj.checkup:
            date_str = str(obj.checkup.date) if obj.checkup.date else '未设置'
            return format_html(
                '<span title="{}">{}</span>',
                obj.checkup.location,
                date_str
            )
        return '未关联'
    checkup_info.short_description = '关联产检'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user', 'checkup')


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    """事项/事件管理"""

    list_display = ['title', 'user_email', 'event_datetime', 'location', 'status_display', 'created_at']
    list_filter = ['date', 'created_at', 'user']
    search_fields = ['title', 'location', 'notes', 'user__email']
    ordering = ['-date', '-start_time']
    readonly_fields = ['duration_display', 'status_display', 'created_at', 'updated_at']
    date_hierarchy = 'date'

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'title', 'date')
        }),
        ('时间安排', {
            'fields': ('start_time', 'end_time', 'duration_display'),
            'description': '设置事项的开始和结束时间'
        }),
        ('地点和出行', {
            'fields': ('location', 'travel_duration'),
            'classes': ('collapse',)
        }),
        ('提醒设置', {
            'fields': ('reminder_minutes',),
            'classes': ('collapse',),
            'description': '设置提前多少分钟提醒'
        }),
        ('备注信息', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        ('状态信息', {
            'fields': ('status_display',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def user_email(self, obj):
        """显示用户邮箱"""
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def event_datetime(self, obj):
        """显示事项时间"""
        if obj.start_time and obj.end_time:
            return format_html(
                '<span title="结束时间: {}">{}</span>',
                obj.end_time.strftime("%Y-%m-%d %H:%M"),
                obj.start_time.strftime("%Y-%m-%d %H:%M")
            )
        elif obj.start_time:
            return obj.start_time.strftime("%Y-%m-%d %H:%M")
        return '-'
    event_datetime.short_description = '事项时间'

    def duration_display(self, obj):
        """显示持续时间"""
        duration = obj.duration
        if duration:
            hours = duration.total_seconds() // 3600
            minutes = (duration.total_seconds() % 3600) // 60
            if hours > 0:
                return f'{int(hours)}小时{int(minutes)}分钟'
            else:
                return f'{int(minutes)}分钟'
        return '未设置'
    duration_display.short_description = '持续时间'

    def status_display(self, obj):
        """显示事项状态"""
        if obj.is_ongoing:
            return format_html('<span style="color: green;">进行中</span>')
        elif obj.is_upcoming:
            return format_html('<span style="color: blue;">即将开始</span>')
        elif obj.is_past:
            return format_html('<span style="color: gray;">已结束</span>')
        return '未知'
    status_display.short_description = '状态'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')
