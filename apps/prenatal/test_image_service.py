from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from apps.users.models import UserAccount
from .models import PrenatalCheckup
from .image_models import PrenatalImage, ImageUploadSession
import tempfile
import os
from PIL import Image
import io


class PrenatalImageModelTest(TestCase):
    """产检图片模型测试"""
    
    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建测试图片
        self.test_image = self.create_test_image()
        
        # 创建产检记录
        future_datetime = timezone.now() + timezone.timedelta(days=7)
        self.checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )
    
    def create_test_image(self):
        """创建测试图片文件"""
        # 创建一个简单的测试图片
        image = Image.new('RGB', (100, 100), color='red')
        image_io = io.BytesIO()
        image.save(image_io, format='JPEG')
        image_io.seek(0)
        
        return SimpleUploadedFile(
            name='test_image.jpg',
            content=image_io.getvalue(),
            content_type='image/jpeg'
        )
    
    def test_create_prenatal_image(self):
        """测试创建产检图片"""
        prenatal_image = PrenatalImage.objects.create(
            user=self.user,
            image=self.test_image,
            image_type='ultrasound',
            title='测试B超图片',
            description='这是一个测试图片',
            checkup=self.checkup
        )
        
        self.assertEqual(prenatal_image.user, self.user)
        self.assertEqual(prenatal_image.image_type, 'ultrasound')
        self.assertEqual(prenatal_image.title, '测试B超图片')
        self.assertEqual(prenatal_image.checkup, self.checkup)
        self.assertTrue(prenatal_image.image_url)
        self.assertGreater(prenatal_image.file_size, 0)
    
    def test_image_metadata_extraction(self):
        """测试图片元数据提取"""
        prenatal_image = PrenatalImage.objects.create(
            user=self.user,
            image=self.test_image,
            image_type='other'
        )
        
        # 检查是否自动提取了图片尺寸
        self.assertEqual(prenatal_image.width, 100)
        self.assertEqual(prenatal_image.height, 100)
        self.assertGreater(prenatal_image.file_size, 0)
    
    def test_file_size_human_property(self):
        """测试人类可读文件大小属性"""
        prenatal_image = PrenatalImage.objects.create(
            user=self.user,
            image=self.test_image,
            image_type='other'
        )
        
        # 测试文件大小格式化
        human_size = prenatal_image.file_size_human
        self.assertTrue(human_size.endswith(('B', 'KB', 'MB')))
    
    def test_image_deletion(self):
        """测试图片删除"""
        prenatal_image = PrenatalImage.objects.create(
            user=self.user,
            image=self.test_image,
            image_type='other'
        )
        
        # 获取文件路径
        image_path = prenatal_image.image.path
        
        # 删除图片记录
        prenatal_image.delete()
        
        # 验证文件是否被删除（注意：在测试环境中可能不会实际删除文件）
        self.assertFalse(PrenatalImage.objects.filter(id=prenatal_image.id).exists())


class ImageUploadSessionModelTest(TestCase):
    """图片上传会话模型测试"""
    
    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_create_upload_session(self):
        """测试创建上传会话"""
        session = ImageUploadSession.objects.create(
            user=self.user,
            session_name='测试上传会话',
            total_files=5,
            status='pending'
        )
        
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.session_name, '测试上传会话')
        self.assertEqual(session.total_files, 5)
        self.assertEqual(session.uploaded_files, 0)
        self.assertEqual(session.failed_files, 0)
        self.assertEqual(session.status, 'pending')
    
    def test_progress_calculation(self):
        """测试进度计算"""
        session = ImageUploadSession.objects.create(
            user=self.user,
            total_files=10,
            uploaded_files=3,
            status='uploading'
        )
        
        self.assertEqual(session.progress_percentage, 30.0)
        self.assertFalse(session.is_completed)
    
    def test_session_completion(self):
        """测试会话完成"""
        session = ImageUploadSession.objects.create(
            user=self.user,
            total_files=3,
            status='uploading'
        )
        
        # 模拟上传过程
        session.increment_uploaded()
        self.assertEqual(session.uploaded_files, 1)
        self.assertEqual(session.status, 'uploading')
        
        session.increment_uploaded()
        self.assertEqual(session.uploaded_files, 2)
        
        session.increment_uploaded()
        self.assertEqual(session.uploaded_files, 3)
        self.assertEqual(session.status, 'completed')
        self.assertTrue(session.is_completed)
        self.assertIsNotNone(session.completed_at)
    
    def test_failed_upload_tracking(self):
        """测试失败上传跟踪"""
        session = ImageUploadSession.objects.create(
            user=self.user,
            total_files=5,
            status='uploading'
        )
        
        # 模拟部分失败
        session.increment_uploaded()
        session.increment_failed()
        session.increment_uploaded()
        
        self.assertEqual(session.uploaded_files, 2)
        self.assertEqual(session.failed_files, 1)
        self.assertEqual(session.progress_percentage, 40.0)


class PrenatalImageIntegrationTest(TestCase):
    """产检图片集成测试"""
    
    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        future_datetime = timezone.now() + timezone.timedelta(days=7)
        self.checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )
    
    def create_test_image(self, name='test.jpg'):
        """创建测试图片"""
        image = Image.new('RGB', (100, 100), color='blue')
        image_io = io.BytesIO()
        image.save(image_io, format='JPEG')
        image_io.seek(0)
        
        return SimpleUploadedFile(
            name=name,
            content=image_io.getvalue(),
            content_type='image/jpeg'
        )
    
    def test_image_checkup_association(self):
        """测试图片与产检记录的关联"""
        # 创建图片
        image = PrenatalImage.objects.create(
            user=self.user,
            image=self.create_test_image(),
            image_type='checkup_report',
            title='产检报告',
            checkup=self.checkup
        )
        
        # 验证关联
        self.assertEqual(image.checkup, self.checkup)
        self.assertIn(image, self.checkup.image_files.all())
    
    def test_multiple_images_per_checkup(self):
        """测试一个产检记录关联多张图片"""
        images = []
        for i in range(3):
            image = PrenatalImage.objects.create(
                user=self.user,
                image=self.create_test_image(f'test_{i}.jpg'),
                image_type='ultrasound',
                title=f'B超图片 {i+1}',
                checkup=self.checkup
            )
            images.append(image)
        
        # 验证关联
        checkup_images = self.checkup.image_files.all()
        self.assertEqual(checkup_images.count(), 3)
        
        for image in images:
            self.assertIn(image, checkup_images)
    
    def test_image_without_checkup(self):
        """测试不关联产检记录的图片"""
        image = PrenatalImage.objects.create(
            user=self.user,
            image=self.create_test_image(),
            image_type='other',
            title='独立图片'
        )
        
        self.assertIsNone(image.checkup)
        self.assertEqual(self.checkup.image_files.count(), 0)
